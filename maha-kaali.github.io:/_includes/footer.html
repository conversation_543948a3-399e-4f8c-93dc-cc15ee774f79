<footer class="mt-auto py-3 text-center">

  <small class="text-muted mb-2">
    <i class="fas fa-code"></i> with <i class="fas fa-heart"></i>
    by <strong>{{ site.author.name | default: "<PERSON><PERSON><PERSON>" }}</strong>
  </small>

  <div class="container-fluid justify-content-center">
    {%- for social in site.data.social-media -%}
      {%- assign username = social[1].username | default: social[1] -%}
      {%- if social[0] == 'email' -%}
        {%- assign username = site.author.email -%}
      {%- endif -%}
      <a class="social mx-1" href="{{ social[1].url }}{{ username }}"
         style="color: #6c757d"
         onMouseOver="this.style.color='#{{ social[1].color }}'"
         onMouseOut="this.style.color='#6c757d'">
        <i class="{{ social[1].icon }} fa-1x"></i>
      </a>
    {%- endfor -%}
  </div>

  <small id="attribution">
    theme <a href="https://github.com/yousinix/portfolYOU">portfolYOU</a>
  </small>

</footer>
