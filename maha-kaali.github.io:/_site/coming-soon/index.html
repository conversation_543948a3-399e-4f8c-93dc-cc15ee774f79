<!DOCTYPE html>

<!--
  portfolYOU Jekyll theme by yousinix
  Free for personal and commercial use under the MIT license
  https://github.com/yousinix/portfolYOU
-->

<html lang="en" class="h-100">

<head>

  
  
  

  

  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1">

  <meta property="og:type" content="website">
  <meta property="og:title" content="Coming Soon">
  <meta property="og:description" content="I build with freedom, serve with heart, and rise without limits.">
  <meta property="og:image" content="https://i.imgur.com/uyDNQnn.jpg">

  <title>Coming Soon</title>
  <meta name="description" content="I build with freedom, serve with heart, and rise without limits.">

  <link rel="shortcut icon" type="image/x-icon" href="/portfolYOU/assets/favicon.ico">

  <!-- Theme style -->
  <script src="/portfolYOU/assets/js/theme.js"></script>

  <!-- Font Awesome CDN -->
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.4/css/all.css">

  <!-- Bootstrap CSS CDN -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">

  <!-- Animate CSS CDN -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/3.7.0/animate.css">

  <!-- Custom CSS -->
  <link rel="stylesheet" href="/portfolYOU/assets/css/style.css">

</head>


<body class="h-100 d-flex flex-column">

  <main class="flex-shrink-0 container mt-5">
    <nav class="navbar navbar-expand-lg navbar-themed">

  <a class="navbar-brand" href="/portfolYOU/"><h5><b>Jayasree M</b></h5></a>

  <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNavAltMarkup" aria-controls="navbarNavAltMarkup" aria-expanded="false" aria-label="Toggle navigation">
    <i class="fas fa-1x fa-bars text-themed"></i>
  </button>

  <div class="collapse navbar-collapse" id="navbarNavAltMarkup">
    <div class="navbar-nav ml-auto"><a class="nav-item nav-link active" href="/portfolYOU/coming-soon/">Coming Soon</a>

      <a class="nav-item nav-link " href="/portfolYOU/projects/">Projects</a>

      <a class="nav-item nav-link " href="/portfolYOU/blog/">Blog</a>

      <a class="nav-item nav-link " href="/portfolYOU/about/">About</a>

      

      <span id="theme-toggler" class="nav-item nav-link" role="button" onclick="toggleTheme()"></span>
    </div>
  </div>

</nav>
    <div class="container-fluid text-center py-5">
  <div class="row justify-content-center">
    <div class="col-lg-8 col-md-10">
      
      <!-- Cute animated icon -->
      <div class="mb-4">
        <div class="coming-soon-icon">
          🚀
        </div>
      </div>
      
      <!-- Main heading -->
      <h1 class="display-4 mb-4 wow animated fadeInDown" data-wow-delay=".1s">
        <strong>Coming Soon!</strong>
      </h1>
      
      <!-- Subtitle -->
      <p class="lead text-muted mb-4 wow animated fadeInUp" data-wow-delay=".2s">
        Something amazing is brewing behind the scenes...
      </p>
      
      <!-- Cute message -->
      <div class="coming-soon-message mb-5 wow animated fadeInUp" data-wow-delay=".3s">
        <p class="h5 text-themed mb-3">
          ✨ I'm working hard to bring you something special! ✨
        </p>
        <p class="text-muted">
          This section is currently under construction. Check back soon for exciting updates!
        </p>
      </div>
      
      <!-- Progress bar animation -->
      <div class="progress-container mb-4 wow animated fadeInUp" data-wow-delay=".4s">
        <p class="small text-muted mb-2">Progress</p>
        <div class="progress" style="height: 8px;">
          <div class="progress-bar progress-bar-striped progress-bar-animated bg-primary" 
               role="progressbar" 
               style="width: 75%;" 
               aria-valuenow="75" 
               aria-valuemin="0" 
               aria-valuemax="100">
          </div>
        </div>
        <p class="small text-muted mt-2">75% Complete</p>
      </div>
      
      <!-- Call to action -->
      <div class="wow animated fadeInUp" data-wow-delay=".5s">
        <a href="/portfolYOU/" class="btn btn-primary btn-lg rounded-pill px-4">
          <i class="fas fa-home mr-2"></i>
          Back to Home
        </a>
      </div>
      
      <!-- Cute footer message -->
      <div class="mt-5 wow animated fadeInUp" data-wow-delay=".6s">
        <p class="small text-muted">
          <i class="fas fa-heart text-danger"></i>
          Made with love and lots of coffee
          <i class="fas fa-coffee text-warning"></i>
        </p>
      </div>
      
    </div>
  </div>
</div>

<!-- Custom styles for coming soon page -->
<style>
.coming-soon-icon {
  font-size: 5rem;
  animation: bounce 2s infinite;
  display: inline-block;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-20px);
  }
  60% {
    transform: translateY(-10px);
  }
}

.coming-soon-message {
  background: rgba(0, 123, 255, 0.1);
  border-radius: 15px;
  padding: 2rem;
  border: 2px dashed rgba(0, 123, 255, 0.3);
}

.progress-container {
  max-width: 400px;
  margin: 0 auto;
}

.btn-primary {
  transition: all 0.3s ease;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

/* Dark theme support */
[data-theme="dark"] .coming-soon-message {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
}
</style>

  </main>
  <footer class="mt-auto py-3 text-center">

  <small class="text-muted mb-2">
    <i class="fas fa-code"></i> with <i class="fas fa-heart"></i>
    by <strong>John Doe</strong>
  </small>

  <div class="container-fluid justify-content-center"><a class="social mx-1" href="mailto:<EMAIL>"
         style="color: #6c757d"
         onMouseOver="this.style.color='#db4437'"
         onMouseOut="this.style.color='#6c757d'">
        <i class="fas fa-envelope fa-1x"></i>
      </a><a class="social mx-1" href="https://www.github.com/{"url"=>"https://www.github.com/", "icon"=>"fab fa-github", "color"=>333333}"
         style="color: #6c757d"
         onMouseOver="this.style.color='#333333'"
         onMouseOut="this.style.color='#6c757d'">
        <i class="fab fa-github fa-1x"></i>
      </a><a class="social mx-1" href="https://www.linkedin.com/in/{"url"=>"https://www.linkedin.com/in/", "icon"=>"fab fa-linkedin-in", "color"=>"007bb5"}"
         style="color: #6c757d"
         onMouseOver="this.style.color='#007bb5'"
         onMouseOut="this.style.color='#6c757d'">
        <i class="fab fa-linkedin-in fa-1x"></i>
      </a></div>

  <small id="attribution">
    theme <a href="https://github.com/yousinix/portfolYOU">portfolYOU</a>
  </small>

</footer>


  
  <!-- GitHub Buttons -->
<script async defer src="https://buttons.github.io/buttons.js"></script>

<!-- jQuery CDN -->
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>

<!-- Popper.js CDN -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.6/umd/popper.min.js"></script>

<!-- Bootstrap JS CDN -->
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js"></script>

<!-- wow.js CDN & Activation -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/wow/1.1.2/wow.js"></script>
<script> new WOW().init(); </script>

<!-- Initialize all tooltips -->
<script>
$(function () {
    $('[data-toggle="tooltip"]').tooltip()
})
</script>
</body>

</html>