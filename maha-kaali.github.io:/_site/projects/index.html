<!DOCTYPE html>

<!--
  portfolYOU Jekyll theme by yousinix
  Free for personal and commercial use under the MIT license
  https://github.com/yousinix/portfolYOU
-->

<html lang="en" class="h-100">

<head>

  
  
  

  

  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1">

  <meta property="og:type" content="website">
  <meta property="og:title" content="Projects">
  <meta property="og:description" content="I build with freedom, serve with heart, and rise without limits.">
  <meta property="og:image" content="https://i.imgur.com/uyDNQnn.jpg">

  <title>Projects</title>
  <meta name="description" content="I build with freedom, serve with heart, and rise without limits.">

  <link rel="shortcut icon" type="image/x-icon" href="/portfolYOU/assets/favicon.ico">

  <!-- Theme style -->
  <script src="/portfolYOU/assets/js/theme.js"></script>

  <!-- Font Awesome CDN -->
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.4/css/all.css">

  <!-- Bootstrap CSS CDN -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">

  <!-- Animate CSS CDN -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/3.7.0/animate.css">

  <!-- Custom CSS -->
  <link rel="stylesheet" href="/portfolYOU/assets/css/style.css">

</head>


<body class="h-100 d-flex flex-column">

  <main class="flex-shrink-0 container mt-5">
    <nav class="navbar navbar-expand-lg navbar-themed">

  <a class="navbar-brand" href="/portfolYOU/"><h5><b>Jayasree M</b></h5></a>

  <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNavAltMarkup" aria-controls="navbarNavAltMarkup" aria-expanded="false" aria-label="Toggle navigation">
    <i class="fas fa-1x fa-bars text-themed"></i>
  </button>

  <div class="collapse navbar-collapse" id="navbarNavAltMarkup">
    <div class="navbar-nav ml-auto"><a class="nav-item nav-link active" href="/portfolYOU/projects/">Projects</a>

      <a class="nav-item nav-link " href="/portfolYOU/blog/">Blog</a>

      <a class="nav-item nav-link " href="/portfolYOU/about/">About</a>

      

      <span id="theme-toggler" class="nav-item nav-link" role="button" onclick="toggleTheme()"></span>
    </div>
  </div>

</nav>
    
<div class="card-columns m-3 mt-5">

  <!-- Remote Projects -->
  

  <!-- Local Projects -->
  <div class="wow animated fadeIn" data-wow-delay=".15s">
  <a href="https://www.google.com" class="project card text-themed"><img id="placeholder-project-img" class="card-img-top" src="https://www.sketchappsources.com/resources/source-image/project-neon-groove-music-ui.png" alt="PlaceHolder Project" /><div class="card-body">
      <h5 id="placeholder-project-name" class="card-title">PlaceHolder Project
      </h5>
      <p id="placeholder-project-desc" class="card-text">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
      <p id="placeholder-project-tools" class="card-text"><span class="badge badge-pill text-primary border border-primary ml-1">nothing</span><span class="badge badge-pill text-primary border border-primary ml-1">important</span></p>
    </div>
  </a>
</div>

  <div class="wow animated fadeIn" data-wow-delay=".15s">
  <a href="/portfolYOU/projects/2-awesome-project" class="project card text-themed"><img id="the-movies-project-img" class="card-img-top" src="https://www.sketchappsources.com/resources/source-image/movie-badges-jurajjurik.png" alt="The Movies Project" /><div class="card-body">
      <h5 id="the-movies-project-name" class="card-title">The Movies Project
      </h5>
      <p id="the-movies-project-desc" class="card-text">This project has an individual showcase page, not just a direct link to the project site or repo. Now you have more space to describe your awesome project!</p>
      <p id="the-movies-project-tools" class="card-text"><span class="badge badge-pill text-primary border border-primary ml-1">C#</span><span class="badge badge-pill text-primary border border-primary ml-1">XML</span><span class="badge badge-pill text-primary border border-primary ml-1">WPF</span></p>
    </div>
  </a>
</div>

  <div class="wow animated fadeIn" data-wow-delay=".15s">
  <a href="https://github.com/YoussefRaafatNasry" class="project card text-themed"><div class="card-body">
      <h5 id="empty-project-name" class="card-title">Empty Project
      </h5>
      <p id="empty-project-desc" class="card-text">This project has no image or showcase page, but it is still a beautiful project inside out!</p>
      <p id="empty-project-tools" class="card-text"><span class="badge badge-pill text-primary border border-primary ml-1">Node JS</span><span class="badge badge-pill text-primary border border-primary ml-1">JavaScript</span><span class="badge badge-pill text-primary border border-primary ml-1">HTML</span><span class="badge badge-pill text-primary border border-primary ml-1">CSS</span></p>
    </div>
  </a>
</div>

  <div class="wow animated fadeIn" data-wow-delay=".15s">
  <a href="https://github.com/YoussefRaafatNasry" class="project card text-themed"><div class="card-body">
      <h5 id="coding-like-vincent-van-gogh-name" class="card-title">Coding Like Vincent Van Gogh
      </h5>
      <p id="coding-like-vincent-van-gogh-desc" class="card-text">Show some support by following me!</p>
      <p id="coding-like-vincent-van-gogh-tools" class="card-text"><span class="badge badge-pill text-primary border border-primary ml-1">Support</span><span class="badge badge-pill text-primary border border-primary ml-1">Author</span><span class="badge badge-pill text-primary border border-primary ml-1">VVG</span></p>
    </div>
  </a>
</div>

  <div class="wow animated fadeIn" data-wow-delay=".15s">
  <a href="https://thefabulous.co/" class="project card text-themed"><img id="the-fabulous-img" class="card-img-top" src="https://storage.googleapis.com/gd-wagtail-prod-assets/original_images/material_design_awards_inline_002.jpg" alt="The Fabulous" /><div class="card-body">
      <h5 id="the-fabulous-name" class="card-title">The Fabulous
      </h5>
      <p id="the-fabulous-desc" class="card-text">Fabulous is a science-based app, incubated in Duke's Behavioral Economics Lab, that will help you build healthy rituals into your life, just like an elite athlete.</p>
      <p id="the-fabulous-tools" class="card-text"><span class="badge badge-pill text-primary border border-primary ml-1">Android</span><span class="badge badge-pill text-primary border border-primary ml-1">Material Design</span></p>
    </div>
  </a>
</div>

  <div class="wow animated fadeIn" data-wow-delay=".15s">
  <a href="https://www.google.com" class="project card text-themed"><img id="dummy-project-img" class="card-img-top" src="https://www.sketchappsources.com/resources/source-image/coo-app-concept-subgaurav.jpg" alt="Dummy Project" /><div class="card-body">
      <h5 id="dummy-project-name" class="card-title">Dummy Project
      </h5>
      <p id="dummy-project-desc" class="card-text">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
      <p id="dummy-project-tools" class="card-text"><span class="badge badge-pill text-primary border border-primary ml-1">Ignore me</span></p>
    </div>
  </a>
</div>

  

</div>

  </main>
  <footer class="mt-auto py-3 text-center">

  <small class="text-muted mb-2">
    <i class="fas fa-code"></i> with <i class="fas fa-heart"></i>
    by <strong>John Doe</strong>
  </small>

  <div class="container-fluid justify-content-center"><a class="social mx-1"  href="mailto:<EMAIL>"
       style="color: #6c757d"
       onMouseOver="this.style.color='#db4437'"
       onMouseOut="this.style.color='#6c757d'">
      <i class="fas fa-envelope fa-1x"></i>
    </a><a class="social mx-1"  href="https://www.github.com/your_username"
       style="color: #6c757d"
       onMouseOver="this.style.color='#333333'"
       onMouseOut="this.style.color='#6c757d'">
      <i class="fab fa-github fa-1x"></i>
    </a><a class="social mx-1"  href="https://www.linkedin.com/in/your_username"
       style="color: #6c757d"
       onMouseOver="this.style.color='#007bb5'"
       onMouseOut="this.style.color='#6c757d'">
      <i class="fab fa-linkedin-in fa-1x"></i>
    </a>

</div><small id="attribution">
    theme <a href="https://github.com/yousinix/portfolYOU">portfolYOU</a>
  </small>

</footer>

  
  <!-- GitHub Buttons -->
<script async defer src="https://buttons.github.io/buttons.js"></script>

<!-- jQuery CDN -->
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>

<!-- Popper.js CDN -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.6/umd/popper.min.js"></script>

<!-- Bootstrap JS CDN -->
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js"></script>

<!-- wow.js CDN & Activation -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/wow/1.1.2/wow.js"></script>
<script> new WOW().init(); </script>

<!-- Initialize all tooltips -->
<script>
$(function () {
    $('[data-toggle="tooltip"]').tooltip()
})
</script>
</body>

</html>