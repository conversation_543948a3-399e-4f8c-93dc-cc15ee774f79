<!DOCTYPE html>

<!--
  portfolYOU Jekyll theme by yousinix
  Free for personal and commercial use under the MIT license
  https://github.com/yousinix/portfolYOU
-->

<html lang="en" class="h-100">

<head>

  
  
  

  

  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1">

  <meta property="og:type" content="website">
  <meta property="og:title" content="About">
  <meta property="og:description" content="I build with freedom, serve with heart, and rise without limits.">
  <meta property="og:image" content="https://i.imgur.com/uyDNQnn.jpg">

  <title>About</title>
  <meta name="description" content="I build with freedom, serve with heart, and rise without limits.">

  <link rel="shortcut icon" type="image/x-icon" href="/portfolYOU/assets/favicon.ico">

  <!-- Theme style -->
  <script src="/portfolYOU/assets/js/theme.js"></script>

  <!-- Font Awesome CDN -->
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.4/css/all.css">

  <!-- Bootstrap CSS CDN -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">

  <!-- Animate CSS CDN -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/3.7.0/animate.css">

  <!-- Custom CSS -->
  <link rel="stylesheet" href="/portfolYOU/assets/css/style.css">

</head>


<body class="h-100 d-flex flex-column">

  <main class="flex-shrink-0 container mt-5">
    <nav class="navbar navbar-expand-lg navbar-themed">

  <a class="navbar-brand" href="/portfolYOU/"><h5><b>Jayasree M</b></h5></a>

  <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNavAltMarkup" aria-controls="navbarNavAltMarkup" aria-expanded="false" aria-label="Toggle navigation">
    <i class="fas fa-1x fa-bars text-themed"></i>
  </button>

  <div class="collapse navbar-collapse" id="navbarNavAltMarkup">
    <div class="navbar-nav ml-auto">
<a class="nav-item nav-link " href="/portfolYOU/coming-soon/">Coming Soon</a>

      <a class="nav-item nav-link " href="/portfolYOU/projects/">Projects</a>

      <a class="nav-item nav-link " href="/portfolYOU/blog/">Blog</a>

      <a class="nav-item nav-link active" href="/portfolYOU/about/">About</a>

      

      <span id="theme-toggler" class="nav-item nav-link" role="button" onclick="toggleTheme()"></span>
    </div>
  </div>

</nav>
    <div class="col-lg-10 mx-auto mt-5 markdown-body">
  <h1 id="about-me"><strong>About Me</strong></h1>

<p>Hi I am <b>Jayasree </b><img class="emoji" title=":wave:" alt=":wave:" src="https://github.githubassets.com/images/icons/emoji/unicode/1f44b.png" height="20" width="20">,<br></p>
<p align="justify">
Part builder, Part thinker, Full-time curious human. <br><br>
I’m drawn to the kind of problems where AI has to make decisions, learn on the fly, or outsmart chaos (especially when security’s on the line). My current fascinations include reinforcement learning, autonomous agents, and the space where machine learning meets cybersecurity.<br><br>

I don’t claim to have all the answers—just a growing list of questions and the drive to chase them down. Whether it’s a stubborn bug, a fuzzy idea, or a weird edge case, I’m usually the one poking at it until it makes sense.
</p>
<!-- <div class="row">
<div class="col-lg">

  <h2 class="mb-3">Programming Skills</h2>

  

</div>
<div class="col-lg">

  <h2 class="mb-3">Other Skills</h2>

  

</div>
</div> -->

<div class="row">
<div class="col mt-4">
  <div class="timeline-body bg-themed">
    
      <div class="timeline-item">
        <div class="content">
          <h2>MS by Research, CMInDs, IIT Bombay</h2>
          <h6 class="date">2025 — Present</h6>
          <p>Deeply curious about the space where AI and cybersecurity intersect — where intelligent systems don't just learn, but also protect. I'm currently exploring paths in secure learning frameworks, reinforcement learning, and autonomous AI agents, with a focus on building systems that can reason, adapt, and act safely in real-world settings.</p>
        </div>
      </div>
    
      <div class="timeline-item">
        <div class="content">
          <h2>BTech in CSE with specialization in AI &amp; ML, VIT Chennai</h2>
          <h6 class="date">2020 — 2024</h6>
          <p>Graduated with a specialization in AI &amp; ML, ranking in the top 6 of my cohort. Built a strong foundation in machine learning, deep learning, and system design—setting the stage for deeper research and applied innovation.</p>
        </div>
      </div>
    
  </div>
</div>
</div>

</div>
  </main>
  <footer class="mt-auto py-3 text-center">

  <small class="text-muted mb-2">
    <i class="fas fa-code"></i> with <i class="fas fa-heart"></i>
    by <strong>John Doe</strong>
  </small>

  <div class="container-fluid justify-content-center">
<a class="social mx-1" href="mailto:<EMAIL>" style="color: #6c757d" onmouseover="this.style.color='#db4437'" onmouseout="this.style.color='#6c757d'">
        <i class="fas fa-envelope fa-1x"></i>
      </a><a class="social mx-1" href="https://www.github.com/%7B" url>"https://www.github.com/", "icon"=&gt;"fab fa-github", "color"=&gt;333333}"
         style="color: #6c757d"
         onMouseOver="this.style.color='#333333'"
         onMouseOut="this.style.color='#6c757d'"&gt;
        <i class="fab fa-github fa-1x"></i>
      </a><a class="social mx-1" href="https://www.linkedin.com/in/%7B" url>"https://www.linkedin.com/in/", "icon"=&gt;"fab fa-linkedin-in", "color"=&gt;"007bb5"}"
         style="color: #6c757d"
         onMouseOver="this.style.color='#007bb5'"
         onMouseOut="this.style.color='#6c757d'"&gt;
        <i class="fab fa-linkedin-in fa-1x"></i>
      </a>
</div>

  <small id="attribution">
    theme <a href="https://github.com/yousinix/portfolYOU">portfolYOU</a>
  </small>

</footer>


  
  <!-- GitHub Buttons -->
<script async defer src="https://buttons.github.io/buttons.js"></script>

<!-- jQuery CDN -->
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>

<!-- Popper.js CDN -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.6/umd/popper.min.js"></script>

<!-- Bootstrap JS CDN -->
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js"></script>

<!-- wow.js CDN & Activation -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/wow/1.1.2/wow.js"></script>
<script> new WOW().init(); </script>

<!-- Initialize all tooltips -->
<script>
$(function () {
    $('[data-toggle="tooltip"]').tooltip()
})
</script>
</body>

</html>