<!DOCTYPE html>

<!--
  portfolYOU Jekyll theme by yousinix
  Free for personal and commercial use under the MIT license
  https://github.com/yousinix/portfolYOU
-->

<html lang="en" class="h-100">

<head>

  
  
  

  

  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1">

  <meta property="og:type" content="website">
  <meta property="og:title" content="A better Hello World">
  <meta property="og:description" content="A different “hello world” to demonstrate language features better.">
  <meta property="og:image" content="https://i.imgur.com/uyDNQnn.jpg">

  <title>A better Hello World</title>
  <meta name="description" content="A different “hello world” to demonstrate language features better.">

  <link rel="shortcut icon" type="image/x-icon" href="/portfolYOU/assets/favicon.ico">

  <!-- Theme style -->
  <script src="/portfolYOU/assets/js/theme.js"></script>

  <!-- Font Awesome CDN -->
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.4/css/all.css">

  <!-- Bootstrap CSS CDN -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">

  <!-- Animate CSS CDN -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/3.7.0/animate.css">

  <!-- Custom CSS -->
  <link rel="stylesheet" href="/portfolYOU/assets/css/style.css">

</head>


<body class="h-100 d-flex flex-column">

  <main class="flex-shrink-0 container mt-5">
    <nav class="navbar navbar-expand-lg navbar-themed">

  <a class="navbar-brand" href="/portfolYOU/"><h5><b>Jayasree M</b></h5></a>

  <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNavAltMarkup" aria-controls="navbarNavAltMarkup" aria-expanded="false" aria-label="Toggle navigation">
    <i class="fas fa-1x fa-bars text-themed"></i>
  </button>

  <div class="collapse navbar-collapse" id="navbarNavAltMarkup">
    <div class="navbar-nav ml-auto"><a class="nav-item nav-link " href="/portfolYOU/coming-soon/">Coming Soon</a>

      <a class="nav-item nav-link " href="/portfolYOU/projects/">Projects</a>

      <a class="nav-item nav-link active" href="/portfolYOU/blog/">Blog</a>

      <a class="nav-item nav-link " href="/portfolYOU/about/">About</a>

      

      <span id="theme-toggler" class="nav-item nav-link" role="button" onclick="toggleTheme()"></span>
    </div>
  </div>

</nav>
    <div class="col-lg-10 mx-auto mt-5 markdown-body">
  <h1><b>A better Hello World</b></h1>

<p class="post-metadata text-muted">
  23 July 2016 -  
  <b>less than 1 min read time</b>

  <br>Tags: 
    
    <a class="text-decoration-none no-underline" href="/portfolYOU/blog/tags#software-development">
      <span class="tag badge badge-pill text-primary border border-primary">Software Development</span>
    </a>
    </p>

<p>Source: <a href="https://ricostacruz.com/til/a-better-hello-world">RICO STA. CRUZ</a></p>

<p>The “Hello world” program is usually the first introduction to any programming language. It looks like this in the C programming language:</p>

<div class="language-c highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="cm">/* hello.c */</span>
<span class="cp">#import &lt;stdio.h&gt;
</span>
<span class="kt">int</span> <span class="nf">main</span><span class="p">(</span><span class="kt">int</span> <span class="n">argc</span><span class="p">,</span> <span class="kt">char</span> <span class="o">*</span><span class="n">argv</span><span class="p">[])</span> <span class="p">{</span>
  <span class="n">printf</span><span class="p">(</span><span class="s">"Hello, world!"</span><span class="p">);</span>
  <span class="k">return</span> <span class="mi">0</span><span class="p">;</span>
<span class="p">}</span>
</code></pre></div></div>

<p>It demonstrates the minimum amount you need to write a C program. In more modern languages however, this example isn’t as useful anymore. Here’s the same example in Python:</p>

<div class="language-python highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="c1"># hello.py
</span><span class="k">print</span> <span class="s">"Hello, world!"</span>
</code></pre></div></div>

<h2 id="a-better-hello-world">A better hello world</h2>

<p>In today’s world of more succint programming languages, we need a different “hello world” to demonstrate language features better. Here’s what I propose:</p>

<div class="language-plaintext highlighter-rouge"><div class="highlight"><pre class="highlight"><code>// hello.js
function getGreeting (name) {
  return `Hello, ${name}!`
}

const message = getGreeting('world')
console.log(message)
</code></pre></div></div>

<p>This simple example demonstrates a few more things than printing strings:</p>

<ul>
  <li>How to write a function with an argument</li>
  <li>Returning values from functions</li>
  <li>How to use variables</li>
  <li>The naming convention for functions (camelCase versus snake_case)</li>
  <li>String concatenation</li>
  <li>Comments</li>
</ul>



</div>
  </main>
  <footer class="mt-auto py-3 text-center">

  <small class="text-muted mb-2">
    <i class="fas fa-code"></i> with <i class="fas fa-heart"></i>
    by <strong>John Doe</strong>
  </small>

  <div class="container-fluid justify-content-center"><a class="social mx-1"  href="mailto:<EMAIL>"
       style="color: #6c757d"
       onMouseOver="this.style.color='#db4437'"
       onMouseOut="this.style.color='#6c757d'">
      <i class="fas fa-envelope fa-1x"></i>
    </a><a class="social mx-1"  href="https://www.github.com/your_username"
       style="color: #6c757d"
       onMouseOver="this.style.color='#333333'"
       onMouseOut="this.style.color='#6c757d'">
      <i class="fab fa-github fa-1x"></i>
    </a><a class="social mx-1"  href="https://www.linkedin.com/in/your_username"
       style="color: #6c757d"
       onMouseOver="this.style.color='#007bb5'"
       onMouseOut="this.style.color='#6c757d'">
      <i class="fab fa-linkedin-in fa-1x"></i>
    </a>

</div><small id="attribution">
    theme <a href="https://github.com/yousinix/portfolYOU">portfolYOU</a>
  </small>

</footer>

  
  <!-- GitHub Buttons -->
<script async defer src="https://buttons.github.io/buttons.js"></script>

<!-- jQuery CDN -->
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>

<!-- Popper.js CDN -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.6/umd/popper.min.js"></script>

<!-- Bootstrap JS CDN -->
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js"></script>

<!-- wow.js CDN & Activation -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/wow/1.1.2/wow.js"></script>
<script> new WOW().init(); </script>

<!-- Initialize all tooltips -->
<script>
$(function () {
    $('[data-toggle="tooltip"]').tooltip()
})
</script>
</body>

</html>