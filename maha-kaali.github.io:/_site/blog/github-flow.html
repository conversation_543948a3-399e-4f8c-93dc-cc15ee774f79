<!DOCTYPE html>

<!--
  portfolYOU Jekyll theme by yousinix
  Free for personal and commercial use under the MIT license
  https://github.com/yousinix/portfolYOU
-->

<html lang="en" class="h-100">

<head>

  
  
  

  

  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1">

  <meta property="og:type" content="website">
  <meta property="og:title" content="GitHub Flow">
  <meta property="og:description" content="GitHub Flow is a lightweight, branch-based workflow that supports teams and projects where deployments are made regularly. This guide explains how and why Gi...">
  <meta property="og:image" content="https://i.imgur.com/uyDNQnn.jpg">

  <title>GitHub Flow</title>
  <meta name="description" content="GitHub Flow is a lightweight, branch-based workflow that supports teams and projects where deployments are made regularly. This guide explains how and why Gi...">

  <link rel="shortcut icon" type="image/x-icon" href="/portfolYOU/assets/favicon.ico">

  <!-- Theme style -->
  <script src="/portfolYOU/assets/js/theme.js"></script>

  <!-- Font Awesome CDN -->
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.4/css/all.css">

  <!-- Bootstrap CSS CDN -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">

  <!-- Animate CSS CDN -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/3.7.0/animate.css">

  <!-- Custom CSS -->
  <link rel="stylesheet" href="/portfolYOU/assets/css/style.css">

</head>


<body class="h-100 d-flex flex-column">

  <main class="flex-shrink-0 container mt-5">
    <nav class="navbar navbar-expand-lg navbar-themed">

  <a class="navbar-brand" href="/portfolYOU/"><h5><b>Jayasree M</b></h5></a>

  <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNavAltMarkup" aria-controls="navbarNavAltMarkup" aria-expanded="false" aria-label="Toggle navigation">
    <i class="fas fa-1x fa-bars text-themed"></i>
  </button>

  <div class="collapse navbar-collapse" id="navbarNavAltMarkup">
    <div class="navbar-nav ml-auto"><a class="nav-item nav-link " href="/portfolYOU/coming-soon/">Coming Soon</a>

      <a class="nav-item nav-link " href="/portfolYOU/projects/">Projects</a>

      <a class="nav-item nav-link active" href="/portfolYOU/blog/">Blog</a>

      <a class="nav-item nav-link " href="/portfolYOU/about/">About</a>

      

      <span id="theme-toggler" class="nav-item nav-link" role="button" onclick="toggleTheme()"></span>
    </div>
  </div>

</nav>
    <div class="col-lg-10 mx-auto mt-5 markdown-body">
  <h1><b>GitHub Flow</b></h1>

<p class="post-metadata text-muted">
  01 January 2017 -  
  <b>4 mins read time</b>

  <br>Tags: 
    
    <a class="text-decoration-none no-underline" href="/portfolYOU/blog/tags#github">
      <span class="tag badge badge-pill text-primary border border-primary">GitHub</span>
    </a>
    
    <a class="text-decoration-none no-underline" href="/portfolYOU/blog/tags#workflow">
      <span class="tag badge badge-pill text-primary border border-primary">Workflow</span>
    </a>
    </p>

<p>Source: <a href="https://guides.github.com/introduction/flow/">GitHub Guides</a></p>

<h2 id="create-a-branch">Create a branch</h2>

<p>When you’re working on a project, you’re going to have a bunch of different features or ideas in progress at any given time – some of which are ready to go, and others which are not. Branching exists to help you manage this workflow.</p>

<p>When you create a branch in your project, you’re creating an environment where you can try out new ideas. Changes you make on a branch don’t affect the <code class="language-plaintext highlighter-rouge">master</code> branch, so you’re free to experiment and commit changes, safe in the knowledge that your branch won’t be merged until it’s ready to be reviewed by someone you’re collaborating with.</p>

<h4 id="protip">ProTip</h4>

<p>Branching is a core concept in Git, and the entire GitHub flow is based upon it. There’s only one rule: anything in the <code class="language-plaintext highlighter-rouge">master</code> branch is always deployable.</p>

<p>Because of this, it’s extremely important that your new branch is created off of master when working on a feature or a fix. Your branch name should be descriptive (e.g., <code class="language-plaintext highlighter-rouge">refactor-authentication</code>, <code class="language-plaintext highlighter-rouge">user-content-cache-key</code>, <code class="language-plaintext highlighter-rouge">make-retina-avatars</code>), so that others can see what is being worked on.</p>

<h2 id="add-commits">Add commits</h2>

<p>Once your branch has been created, it’s time to start making changes. Whenever you add, edit, or delete a file, you’re making a commit, and adding them to your branch. This process of adding commits keeps track of your progress as you work on a feature branch.</p>

<p>Commits also create a transparent history of your work that others can follow to understand what you’ve done and why. Each commit has an associated commit message, which is a description explaining why a particular change was made. Furthermore, each commit is considered a separate unit of change. This lets you roll back changes if a bug is found, or if you decide to head in a different direction.</p>

<h4 id="protip-1">ProTip</h4>

<p>Commit messages are important, especially since Git tracks your changes and then displays them as commits once they’re pushed to the server. By writing clear commit messages, you can make it easier for other people to follow along and provide feedback.</p>

<h2 id="open-a-pull-request">Open a Pull Request</h2>

<p>Pull Requests initiate discussion about your commits. Because they’re tightly integrated with the underlying Git repository, anyone can see exactly what changes would be merged if they accept your request.</p>

<p>You can open a Pull Request at any point during the development process: when you have little or no code but want to share some screenshots or general ideas, when you’re stuck and need help or advice, or when you’re ready for someone to review your work. By using GitHub’s @mention system in your Pull Request message, you can ask for feedback from specific people or teams, whether they’re down the hall or ten time zones away.</p>

<h4 id="protip-2">ProTip</h4>

<p>Pull Requests are useful for contributing to open source projects and for managing changes to shared repositories. If you’re using a Fork &amp; Pull Model, Pull Requests provide a way to notify project maintainers about the changes you’d like them to consider. If you’re using a Shared Repository Model, Pull Requests help start code review and conversation about proposed changes before they’re merged into the master branch.</p>

<h2 id="discuss-and-review-your-code">Discuss and review your code</h2>

<p>Once a Pull Request has been opened, the person or team reviewing your changes may have questions or comments. Perhaps the coding style doesn’t match project guidelines, the change is missing unit tests, or maybe everything looks great and props are in order. Pull Requests are designed to encourage and capture this type of conversation.</p>

<p>You can also continue to push to your branch in light of discussion and feedback about your commits. If someone comments that you forgot to do something or if there is a bug in the code, you can fix it in your branch and push up the change. GitHub will show your new commits and any additional feedback you may receive in the unified Pull Request view.</p>

<h4 id="protip-3">ProTip</h4>

<p>Pull Request comments are written in Markdown, so you can embed images and emoji, use pre-formatted text blocks, and other lightweight formatting.</p>

<h2 id="deploy">Deploy</h2>

<p>With GitHub, you can deploy from a branch for final testing in production before merging to master.</p>

<p>Once your pull request has been reviewed and the branch passes your tests, you can deploy your changes to verify them in production. If your branch causes issues, you can roll it back by deploying the existing master into production.</p>

<h2 id="merge">Merge</h2>

<p>Now that your changes have been verified in production, it is time to merge your code into the master branch.</p>

<p>Once merged, Pull Requests preserve a record of the historical changes to your code. Because they’re searchable, they let anyone go back in time to understand why and how a decision was made.</p>

<h4 id="protip-4">ProTip</h4>

<p>By incorporating certain keywords into the text of your Pull Request, you can associate issues with code. When your Pull Request is merged, the related issues are also closed. For example, entering the phrase <code class="language-plaintext highlighter-rouge">Closes #32</code> would close issue number 32 in the repository.</p>



</div>
  </main>
  <footer class="mt-auto py-3 text-center">

  <small class="text-muted mb-2">
    <i class="fas fa-code"></i> with <i class="fas fa-heart"></i>
    by <strong>John Doe</strong>
  </small>

  <div class="container-fluid justify-content-center"><a class="social mx-1"  href="mailto:<EMAIL>"
       style="color: #6c757d"
       onMouseOver="this.style.color='#db4437'"
       onMouseOut="this.style.color='#6c757d'">
      <i class="fas fa-envelope fa-1x"></i>
    </a><a class="social mx-1"  href="https://www.github.com/your_username"
       style="color: #6c757d"
       onMouseOver="this.style.color='#333333'"
       onMouseOut="this.style.color='#6c757d'">
      <i class="fab fa-github fa-1x"></i>
    </a><a class="social mx-1"  href="https://www.linkedin.com/in/your_username"
       style="color: #6c757d"
       onMouseOver="this.style.color='#007bb5'"
       onMouseOut="this.style.color='#6c757d'">
      <i class="fab fa-linkedin-in fa-1x"></i>
    </a>

</div><small id="attribution">
    theme <a href="https://github.com/yousinix/portfolYOU">portfolYOU</a>
  </small>

</footer>

  
  <!-- GitHub Buttons -->
<script async defer src="https://buttons.github.io/buttons.js"></script>

<!-- jQuery CDN -->
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>

<!-- Popper.js CDN -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.6/umd/popper.min.js"></script>

<!-- Bootstrap JS CDN -->
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js"></script>

<!-- wow.js CDN & Activation -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/wow/1.1.2/wow.js"></script>
<script> new WOW().init(); </script>

<!-- Initialize all tooltips -->
<script>
$(function () {
    $('[data-toggle="tooltip"]').tooltip()
})
</script>
</body>

</html>