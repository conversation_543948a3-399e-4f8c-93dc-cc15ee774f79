# Contribute to portfolYOU

1. [Fork][fork] and clone the repository.
1. Create a new branch based on `master`: `git checkout -b <my-branch-name>`
1. Make your changes, and make sure the site still builds.
1. Push to your fork and [submit a pull request][compare] from your branch to `master`
1. Pat yourself on the back and wait for your pull request to be reviewed.
1. *Here are a few things you have to do:*
   - Write a good commit message.
   - Follow the style guide where possible.
   - Keep your change as focused as possible. If there are multiple changes you would like to make that are not dependent upon each other, consider submitting them as separate pull requests.

[fork]: https://github.com/yousinix/portfolYOU/fork
[compare]: https://github.com/yousinix/portfolYOU/compare
