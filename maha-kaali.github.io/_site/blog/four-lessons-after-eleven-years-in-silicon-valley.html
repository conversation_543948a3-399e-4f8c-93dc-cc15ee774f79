<!DOCTYPE html>

<!--
  portfolYOU Jekyll theme by yousinix
  Free for personal and commercial use under the MIT license
  https://github.com/yousinix/portfolYOU
-->

<html lang="en" class="h-100">

<head>

  
  
  

  

  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1">

  <meta property="og:type" content="website">
  <meta property="og:title" content="4 Lessons After 11 Years in Silicon Valley">
  <meta property="og:description" content="On the realities of opportunity, success, reputation, and relationships in tech.">
  <meta property="og:image" content="maha-kaali.github.io/_data/me.png">

  <title>4 Lessons After 11 Years in Silicon Valley</title>
  <meta name="description" content="On the realities of opportunity, success, reputation, and relationships in tech.">

  <link rel="shortcut icon" type="image/x-icon" href="/portfolio/assets/favicon.ico">

  <!-- Theme style -->
  <script src="/portfolio/assets/js/theme.js"></script>

  <!-- Font Awesome CDN -->
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.4/css/all.css">

  <!-- Bootstrap CSS CDN -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">

  <!-- Animate CSS CDN -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/3.7.0/animate.css">

  <!-- Custom CSS -->
  <link rel="stylesheet" href="/portfolio/assets/css/style.css">

</head>


<body class="h-100 d-flex flex-column">

  <main class="flex-shrink-0 container mt-5">
    <nav class="navbar navbar-expand-lg navbar-themed">

  <a class="navbar-brand" href="/portfolio/"><h5><b>Jayasree M</b></h5></a>

  <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNavAltMarkup" aria-controls="navbarNavAltMarkup" aria-expanded="false" aria-label="Toggle navigation">
    <i class="fas fa-1x fa-bars text-themed"></i>
  </button>

  <div class="collapse navbar-collapse" id="navbarNavAltMarkup">
    <div class="navbar-nav ml-auto"><a class="nav-item nav-link " href="/portfolio/projects/">Projects</a>

      <a class="nav-item nav-link active" href="/portfolio/blog/">Blog</a>

      <a class="nav-item nav-link " href="/portfolio/about/">About</a>

      

      <span id="theme-toggler" class="nav-item nav-link" role="button" onclick="toggleTheme()"></span>
    </div>
  </div>

</nav>
    <div class="col-lg-10 mx-auto mt-5 markdown-body">
  <h1><b>4 Lessons After 11 Years in Silicon Valley</b></h1>

<p class="post-metadata text-muted">
  09 May 2018 -  
  <b>7 mins read time</b>

  <br></p>

<p>Source: <a href="https://medium.com/s/story/four-lessons-after-eleven-years-in-silicon-valley-d87507b7a4f6">Ashley Mayer</a></p>

<p>Silicon Valley is a magical place with some strange norms—perhaps because companies, careers, and fortunes rise and fall with such astounding speed. Here are a few of the quirky, brutal, and hopefully useful lessons I learned during my 11 years living and working in the technology industry’s epicenter.</p>

<h2 id="1-theres-opportunity-in-what-others-undervalue">1. There’s opportunity in what others undervalue</h2>

<p>There’s a rigid hierarchy of functions in Silicon Valley. At the top of the pyramid sit the entrepreneurs, the engineers, the venture capitalists. The closer you are to building or funding, the more respect you get—which probably makes sense. But when I began my career in tech, I wasn’t prepared for how little respect is left over for other functions: recruiting, HR, marketing, communications, etc. There’s an assumption that truly great products market themselves or that truly great companies are magnets for top talent. To work in these superfluous fields is either a sign that your company must compensate for its lack of greatness or that you’re but an intermediary for the inevitable.</p>

<p>Of course, not everyone thinks like this. And that’s where the upside to this warped view comes in. At the company level, it’s quite clear you’ll need to out-innovate your competitors by building a better product. But what about the less obvious vectors for competition? With the benefit of hindsight, it’s easy to see where investments in culture have paid off exceptionally well (and where the lack thereof has halted otherwise unstoppable companies). In nascent industries—especially highly regulated ones—educating customers and stakeholders about your product and market may be as core to survival as the product itself.</p>

<p>This opportunity for differentiation also exists at the individual level. It used to bother me that people made certain assumptions about me based on my profession. I craved validation from my peers and resented the stereotypes that came along with PR. But the longer I’ve been in this field, the more respect I have for how nuanced, impactful, and essential our work is, and consequently, I’m bothered less by other people’s projections. An unfortunate consequence of the hierarchy of functions is that it’s harder to attract top talent to the layers we undervalue, which hurts the industry as a whole. But, as an individual, it means that it’s probably more feasible to distinguish yourself as one of the top recruiters or marketers than it is to become a top engineer in a world where that is the ultimate prize.</p>

<h2 id="2-theres-nothing-more-dangerous-early-in-your-career-than-success">2. There’s nothing more dangerous early in your career than success</h2>

<p>One of our industry’s oft-repeated (and oft-abused) sayings is, “If you’re offered a seat on a rocket ship, you don’t ask what seat. You just get on.” It’s what Google’s then-CEO Eric Schmidt told Sheryl Sandberg to convince her to come onboard in 2001, and I’ve always appreciated the humility embedded in this perspective (for the seat-taker, that is). But we often fail to dwell on the inevitable follow-up question: If you took a seat on that rocket ship, and it was indeed a rocket ship, how do you know if you made any meaningful impact on its speed or trajectory?</p>

<p>Success is one of the most dangerous things that can happen to you early in your career. When you’re on a so-called rocket ship, you’re likely drinking from the fire hose daily, making things up as you go along. If you’re given responsibilities that exceed your experience, you’re probably plagued by self-doubt. Then, at some point, if you’re lucky, the company you’ve helped build is declared a success. And those many bumps along the way are ironed out into a perfect narrative. Perhaps you’re even tempted to believe it.</p>

<blockquote>
  <p>Some reputations are built on much less than you would assume.</p>
</blockquote>

<p>In Silicon Valley, myths about people and companies tend to beat out carefully considered case studies. Maybe it’s because so much creation happens when companies are still private and therefore less observable. Maybe it’s because there are so many new and invisible forces at work (emerging technology trends, cultural and behavioral shifts) in a company’s meteoric rise that mythology is the only way we can make sense of it. Maybe it’s because we love a good story—and a good creation story most of all.</p>

<p>It’s a balancing act to allow yourself pride in what you’ve helped accomplish without getting caught up in your own personal mythology. Be grateful for the tough times: They will keep you anchored during headier cycles. If you battle insecurity and anxiety on the regular (raises hand), find solace in the fact that you’re probably working harder than you would if you were capable of believing that it was your seat that made all the difference.</p>

<h2 id="3-some-reputations-are-built-on-surprisingly-little">3. Some reputations are built on surprisingly little</h2>

<p>This is going to seem random, but bear with me: In the 1999 rom-com Never Been Kissed, Drew Barrymore’s character, Josie, is a reporter who goes undercover as a high school student to write about the “cool” high school crowd. But there’s just one problem: She’s super uncool, so she can’t get anywhere near them. Then her naturally cool younger brother decides to relive his high school glory days and salvages her assignment by convincing the cool kids that Josie is, in fact, quite cool. “All you need is for one person to think you’re cool,” he tells her. “And you’re in.”</p>

<p>Silicon Valley can feel a little like high school—in many ways, but especially when it comes to people’s reputations. I’m regularly shocked by how much just one person declaring someone a “rockstar” can open doors and even change the trajectory of a career. And if the person doing the declaring is particularly influential, other people will repeat their pronouncement as a given. The speed and opacity of startup trajectories make it impossible to really know how impactful someone was (how to separate the seat from the rocket ship), so personal endorsements carry a tremendous amount of weight. Which means that some reputations are built on much less than you would assume.</p>

<p>This is troubling, especially because influential people tend to skew white and male, as do their networks, which only reinforces existing power structures. But it’s also an incredible opportunity to elevate deserving but underappreciated and underrepresented people—especially if you yourself are influential. I doubt many people know how much weight their words carry.</p>

<p>Of course, if you’re not accustomed to wielding this power—or asking for it to be wielded on your behalf—it can feel pretty uncomfortable. Women in particular have a harder time transitioning from the personal and emotional to the transactional in their relationships. My female friends and I have discussed this extensively and have even experimented with a “favor swap” event where the whole point is to get transactional. Maybe this is what Lean In Circles should have been all along—lead with the favors, not the feelings.</p>

<h2 id="4-your-former-co-workers-are-your-rocks-so-keep-them-close">4. Your former co-workers are your rocks, so keep them close</h2>

<p>This one is simple, but important. We all know how critical it is to build strong relationships inside a company, but it wasn’t until I moved on from my first startup job that I realized how incredibly valuable co-worker relationships become after you leave. After years together in the trenches, former co-workers know your strengths and can call you on your bullshit. And once you’re no longer co-workers, all those pesky work-related complications and politics disappear.</p>

<p>Your non-co-worker friends will of course cheer you on, but if you’re in a rut professionally or trying to figure out if you’re the one being difficult in a dysfunctional work relationship, no one can help you troubleshoot like your former colleagues. Same if you need a substantive ego boost. And because your relationship started in a work context, it’s also much easier to be transactional, whether that means asking for intros, references, funding, or feedback.</p>



</div>
  </main>
  <footer class="mt-auto py-3 text-center">

  <small class="text-muted mb-2">
    <i class="fas fa-code"></i> with <i class="fas fa-heart"></i>
    by <strong>Jayasree M</strong>
  </small>

  <div class="container-fluid justify-content-center"><a class="social mx-1"  href="mailto:<EMAIL>"
       style="color: #6c757d"
       onMouseOver="this.style.color='#db4437'"
       onMouseOut="this.style.color='#6c757d'">
      <i class="fas fa-envelope fa-1x"></i>
    </a><a class="social mx-1"  href="https://www.github.com/mahakaali"
       style="color: #6c757d"
       onMouseOver="this.style.color='#333333'"
       onMouseOut="this.style.color='#6c757d'">
      <i class="fab fa-github fa-1x"></i>
    </a><a class="social mx-1"  href="https://www.linkedin.com/in/jayasree-m-ba8101244"
       style="color: #6c757d"
       onMouseOver="this.style.color='#007bb5'"
       onMouseOut="this.style.color='#6c757d'">
      <i class="fab fa-linkedin-in fa-1x"></i>
    </a>

</div><small id="attribution">
    theme <a href="https://github.com/yousinix/portfolYOU">portfolYOU</a>
  </small>

</footer>

  
  <!-- GitHub Buttons -->
<script async defer src="https://buttons.github.io/buttons.js"></script>

<!-- jQuery CDN -->
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>

<!-- Popper.js CDN -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.6/umd/popper.min.js"></script>

<!-- Bootstrap JS CDN -->
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js"></script>

<!-- wow.js CDN & Activation -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/wow/1.1.2/wow.js"></script>
<script> new WOW().init(); </script>

<!-- Initialize all tooltips -->
<script>
$(function () {
    $('[data-toggle="tooltip"]').tooltip()
})
</script>
</body>

</html>