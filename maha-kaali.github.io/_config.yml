### Site Settings ###
title               : <PERSON><PERSON><PERSON> M
description         : >-
                      I build with freedom, serve with heart, and rise without limits.
baseurl             : "/portfolio"                     # Change to empty quotes if you are hosting your site at <your-username>.github.io directly
repository          : maha-kaali/maha-kaali.github.io    # Change to <your-username>/<your-username>.github.io (or remove it if you don't need remote projects)
remote_theme        : YoussefRaafatNasry/portfolYOU


### Plugins ###
plugins:
  - jemoji


### Navbar Settings ###
nav_exclude:                                            # The following paths are excluded from navbar
  - pages/tags.html
  - pages/404.html
  - pages/index.md
  - documentation/partials/**     
  - pages/coming-soon.html                      # For Documentation Only


### Author Info ###
author:
  name              : Jaya<PERSON>ree M
  image             : maha-kaali.github.io/_data/me.png
  # behance           : your_username
# dribbble          : your_username
  email             : <EMAIL>
  # facebook          : your_username
  github            : ma<PERSON><PERSON><PERSON>
# gitlab            : your_username
# instagram         : your_username
# kaggle            : your_username
  linkedin          : jayasree-m-ba8101244
  # medium            : your_username
# soundcloud        : your_username
# spotify           : your_username
# stackoverflow     : your_user_id
# tumblr            : your_username.tumblr.com
# twitch            : your_username
  # twitter           : your_username
# vimeo             : your_username
# youtube           : your_channel_name
# keybase           : your_username


### Posts ###
permalink: /blog/:title


### Collections ###
collections:
  projects:
    output: true
    permalink: /projects/:name


# ### Disqus ###
# disqus:
#   shortname: your-short-name-disqus                     # Your website Shortname on disqus


### Analytics ###
# analytics:
#   enabled: false                                        # Set true to enable analytics
#   google:
#     tracking_id: your-google-tracking-id


### Defaults for collections ###
defaults:
  - scope:
      path: ""
      type: "projects"
    values:
      layout: "page"

  - scope:
      path: ""
      type: "posts"
    values:
      comments: false                                   # Set to true to enable disqus comments

### Exclude from processing ###
exclude:
  - README.md
  - CONTRIBUTING.md
  - LICENSE
  - "*.log"
